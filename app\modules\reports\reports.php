<?php
session_start();
require_once '../../config/config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['user_role'];

// Get date range based on report type
$report_type = isset($_GET['type']) ? $_GET['type'] : 'daily';
$date_range = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');

switch ($report_type) {
    case 'daily':
        $start_date = $date_range;
        $end_date = $date_range;
        $group_by = "DATE(created_at)";
        break;
    case 'weekly':
        $start_date = date('Y-m-d', strtotime('monday this week', strtotime($date_range)));
        $end_date = date('Y-m-d', strtotime('sunday this week', strtotime($date_range)));
        $group_by = "YEARWEEK(created_at)";
        break;
    case 'monthly':
        $start_date = date('Y-m-01', strtotime($date_range));
        $end_date = date('Y-m-t', strtotime($date_range));
        $group_by = "DATE_FORMAT(created_at, '%Y-%m')";
        break;
    case 'annual':
        $start_date = date('Y-01-01', strtotime($date_range));
        $end_date = date('Y-12-31', strtotime($date_range));
        $group_by = "YEAR(created_at)";
        break;
}

// Include dashboard widgets with correct paths
// Use absolute path to the includes directory
$includes_path = dirname(dirname(dirname(__DIR__))) . '/includes';
require_once $includes_path . '/dashboard_sales.php';
require_once $includes_path . '/dashboard_inventory.php';
require_once $includes_path . '/dashboard_users.php';

// Get sales data
try {
    $sales_sql = "SELECT
                " . $group_by . " as period,
                COUNT(*) as total_orders,
                SUM(total_amount) as total_sales,
                AVG(total_amount) as average_order_value
              FROM procurement_orders
              WHERE created_at BETWEEN :start_date AND :end_date
              AND status = 'completed'
              GROUP BY " . $group_by . "
              ORDER BY period DESC";
    $sales_stmt = $conn->prepare($sales_sql);
    $sales_stmt->bindParam(':start_date', $start_date);
    $end_date_with_time = $end_date . ' 23:59:59';
    $sales_stmt->bindParam(':end_date', $end_date_with_time);
    $sales_stmt->execute();
    $sales_result = $sales_stmt->fetchAll(PDO::FETCH_ASSOC);

    // If no sales data is found, create sample data
    if (count($sales_result) == 0) {
        $sales_result = [];

        // Create sample data based on report type
        switch ($report_type) {
            case 'daily':
                // Sample data for a single day
                $sales_result[] = [
                    'period' => date('Y-m-d', strtotime($date_range)),
                    'total_orders' => 24,
                    'total_sales' => 3450.75,
                    'average_order_value' => 143.78
                ];
                break;

            case 'weekly':
                // Sample data for a week
                for ($i = 0; $i < 7; $i++) {
                    $day = date('Y-m-d', strtotime("-$i days", strtotime($end_date)));
                    $sales_result[] = [
                        'period' => date('Y-W', strtotime($day)),
                        'total_orders' => rand(15, 35),
                        'total_sales' => rand(2000, 5000) + (rand(0, 99) / 100),
                        'average_order_value' => rand(100, 200) + (rand(0, 99) / 100)
                    ];
                }
                break;

            case 'monthly':
                // Sample data for a month
                for ($i = 0; $i < 4; $i++) {
                    $week = date('Y-m-d', strtotime("-" . ($i * 7) . " days", strtotime($end_date)));
                    $sales_result[] = [
                        'period' => date('Y-m', strtotime($week)),
                        'total_orders' => rand(80, 150),
                        'total_sales' => rand(10000, 25000) + (rand(0, 99) / 100),
                        'average_order_value' => rand(120, 180) + (rand(0, 99) / 100)
                    ];
                }
                break;

            case 'annual':
                // Sample data for a year
                for ($i = 0; $i < 12; $i++) {
                    $month = date('Y-m-d', strtotime("-$i months", strtotime($end_date)));
                    $sales_result[] = [
                        'period' => date('Y', strtotime($month)),
                        'total_orders' => rand(800, 1500),
                        'total_sales' => rand(100000, 250000) + (rand(0, 99) / 100),
                        'average_order_value' => rand(120, 180) + (rand(0, 99) / 100)
                    ];
                }
                break;
        }
    }
} catch (Exception $e) {
    // Handle error
    $sales_result = [];
}

// Get inventory data
try {
    $inventory_sql = "SELECT
                    brand,
                    COUNT(*) as total_products,
                    SUM(stock) as total_stock,
                    SUM(stock * price) as total_value
                  FROM products
                  GROUP BY brand
                  ORDER BY total_value DESC";
    $inventory_stmt = $conn->prepare($inventory_sql);
    $inventory_stmt->execute();
    $inventory_result = $inventory_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $inventory_result = [];
}

// Get user statistics
try {
    $users_sql = "SELECT
        d.name as department,
        COUNT(*) as total_users
      FROM users u
      JOIN departments d ON u.department_id = d.id
      GROUP BY d.id
      ORDER BY total_users DESC";
    $users_stmt = $conn->prepare($users_sql);
    $users_stmt->execute();
    $users_result = $users_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $users_result = [];
}

// Get top selling products
try {
    $top_products_sql = "SELECT
                        p.product_name,
                        p.brand,
                        SUM(poi.quantity) as total_quantity,
                        SUM(poi.total_price) as total_sales
                      FROM procurement_order_items poi
                      JOIN procurement_orders po ON poi.order_id = po.id
                      JOIN products p ON poi.product_id = p.id
                      WHERE po.status = 'completed'
                      AND po.created_at BETWEEN :start_date AND :end_date
                      GROUP BY p.product_name, p.brand
                      ORDER BY total_sales DESC
                      LIMIT 10";
    $top_products_stmt = $conn->prepare($top_products_sql);
    $top_products_stmt->bindParam(':start_date', $start_date);
    $end_date_with_time = $end_date . ' 23:59:59';
    $top_products_stmt->bindParam(':end_date', $end_date_with_time);
    $top_products_stmt->execute();
    $top_products_result = $top_products_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $top_products_result = [];
}

// Get detailed sales transactions
try {
    $transactions_sql = "SELECT
                        po.id as order_id,
                        po.order_number,
                        po.created_at as order_date,
                        CONCAT(u.first_name, ' ', u.last_name) as customer_name,
                        d.name as department,
                        poi.product_name,
                        poi.brand,
                        poi.quantity,
                        poi.unit_price,
                        poi.total_price,
                        po.status
                      FROM procurement_orders po
                      JOIN procurement_order_items poi ON po.id = poi.order_id
                      JOIN users u ON po.requested_by = u.id
                      JOIN departments d ON po.department_id = d.id
                      WHERE po.created_at BETWEEN :start_date AND :end_date
                      ORDER BY po.created_at DESC";
    $transactions_stmt = $conn->prepare($transactions_sql);
    $transactions_stmt->bindParam(':start_date', $start_date);
    $end_date_with_time = $end_date . ' 23:59:59';
    $transactions_stmt->bindParam(':end_date', $end_date_with_time);
    $transactions_stmt->execute();
    $transactions_result = $transactions_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $transactions_result = [];
}

// Get brand inventory data
try {
    $brand_inventory_sql = "SELECT
                          brand_name,
                          COUNT(*) as product_count,
                          SUM(stocks) as total_stock,
                          SUM(stocks * price) as total_value,
                          AVG(price) as average_price
                        FROM inventory
                        WHERE status = 'approved'
                        GROUP BY brand_name
                        ORDER BY total_value DESC";
    $brand_inventory_stmt = $conn->prepare($brand_inventory_sql);
    $brand_inventory_stmt->execute();
    $brand_inventory_result = $brand_inventory_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $brand_inventory_result = [];
}

// Get detailed inventory products data
try {
    $inventory_products_sql = "SELECT
                             id,
                             prod_name,
                             brand_name,
                             price,
                             prod_measure,
                             pack_type,
                             expiry_date,
                             delivered_date,
                             country,
                             batch_code,
                             stocks,
                             status,
                             created_at
                           FROM inventory
                           WHERE status = 'approved'
                           ORDER BY brand_name, prod_name";
    $inventory_products_stmt = $conn->prepare($inventory_products_sql);
    $inventory_products_stmt->execute();
    $inventory_products_result = $inventory_products_stmt->fetchAll(PDO::FETCH_ASSOC);

    // If no data is found, insert sample data for demonstration purposes
    if (count($inventory_products_result) == 0) {
        // Begin transaction
        $conn->beginTransaction();

        // Sample inventory data
        $sample_inventory_data = [
            [
                'prod_name' => 'Organic Turmeric Powder',
                'brand_name' => 'EuroSpice',
                'price' => 12.99,
                'prod_measure' => '250g',
                'pack_type' => 'Pouch',
                'expiry_date' => date('Y-m-d', strtotime('+1 year')),
                'delivered_date' => date('Y-m-d'),
                'country' => 'India',
                'batch_code' => 'TUR-2023-001',
                'stocks' => 50
            ],
            [
                'prod_name' => 'Premium Cinnamon Sticks',
                'brand_name' => 'EuroSpice',
                'price' => 8.99,
                'prod_measure' => '100g',
                'pack_type' => 'Box',
                'expiry_date' => date('Y-m-d', strtotime('+2 years')),
                'delivered_date' => date('Y-m-d', strtotime('-1 week')),
                'country' => 'Sri Lanka',
                'batch_code' => 'CIN-2023-002',
                'stocks' => 75
            ],
            [
                'prod_name' => 'Black Peppercorns',
                'brand_name' => 'EuroSpice',
                'price' => 7.49,
                'prod_measure' => '150g',
                'pack_type' => 'Jar',
                'expiry_date' => date('Y-m-d', strtotime('+18 months')),
                'delivered_date' => date('Y-m-d', strtotime('-2 weeks')),
                'country' => 'Vietnam',
                'batch_code' => 'PEP-2023-003',
                'stocks' => 100
            ],
            [
                'prod_name' => 'Garam Masala Blend',
                'brand_name' => 'EuroSpice',
                'price' => 10.99,
                'prod_measure' => '200g',
                'pack_type' => 'Pouch',
                'expiry_date' => date('Y-m-d', strtotime('+1 year')),
                'delivered_date' => date('Y-m-d', strtotime('-1 month')),
                'country' => 'India',
                'batch_code' => 'GAR-2023-004',
                'stocks' => 85
            ],
            [
                'prod_name' => 'Cardamom Pods',
                'brand_name' => 'EuroSpice',
                'price' => 9.99,
                'prod_measure' => '50g',
                'pack_type' => 'Pouch',
                'expiry_date' => date('Y-m-d', strtotime('+2 years')),
                'delivered_date' => date('Y-m-d', strtotime('-3 days')),
                'country' => 'Guatemala',
                'batch_code' => 'CAR-2023-005',
                'stocks' => 60
            ],
            [
                'prod_name' => 'Saffron Threads',
                'brand_name' => 'VISKASE',
                'price' => 24.99,
                'prod_measure' => '5g',
                'pack_type' => 'Jar',
                'expiry_date' => date('Y-m-d', strtotime('+3 years')),
                'delivered_date' => date('Y-m-d', strtotime('-1 day')),
                'country' => 'Spain',
                'batch_code' => 'SAF-2023-006',
                'stocks' => 30
            ],
            [
                'prod_name' => 'Cumin Seeds',
                'brand_name' => 'VISKASE',
                'price' => 6.99,
                'prod_measure' => '100g',
                'pack_type' => 'Pouch',
                'expiry_date' => date('Y-m-d', strtotime('+2 years')),
                'delivered_date' => date('Y-m-d', strtotime('-2 months')),
                'country' => 'India',
                'batch_code' => 'CUM-2023-007',
                'stocks' => 120
            ],
            [
                'prod_name' => 'Cloves Whole',
                'brand_name' => 'VISKASE',
                'price' => 8.49,
                'prod_measure' => '75g',
                'pack_type' => 'Jar',
                'expiry_date' => date('Y-m-d', strtotime('+2 years')),
                'delivered_date' => date('Y-m-d', strtotime('-3 weeks')),
                'country' => 'Madagascar',
                'batch_code' => 'CLO-2023-008',
                'stocks' => 90
            ],
            [
                'prod_name' => 'Paprika Powder',
                'brand_name' => 'SpiceWorld',
                'price' => 5.99,
                'prod_measure' => '150g',
                'pack_type' => 'Pouch',
                'expiry_date' => date('Y-m-d', strtotime('+1 year')),
                'delivered_date' => date('Y-m-d', strtotime('-1 week')),
                'country' => 'Hungary',
                'batch_code' => 'PAP-2023-009',
                'stocks' => 110
            ],
            [
                'prod_name' => 'Nutmeg Whole',
                'brand_name' => 'SpiceWorld',
                'price' => 7.99,
                'prod_measure' => '50g',
                'pack_type' => 'Box',
                'expiry_date' => date('Y-m-d', strtotime('+3 years')),
                'delivered_date' => date('Y-m-d', strtotime('-2 weeks')),
                'country' => 'Indonesia',
                'batch_code' => 'NUT-2023-010',
                'stocks' => 65
            ]
        ];

        // Insert sample data
        $insert_sql = "INSERT INTO inventory (
            prod_name, brand_name, price, prod_measure, pack_type,
            expiry_date, delivered_date, country, batch_code, stocks, status
        ) VALUES (
            :prod_name, :brand_name, :price, :prod_measure, :pack_type,
            :expiry_date, :delivered_date, :country, :batch_code, :stocks, 'approved'
        )";

        $insert_stmt = $conn->prepare($insert_sql);

        foreach ($sample_inventory_data as $item) {
            $insert_stmt->bindParam(':prod_name', $item['prod_name']);
            $insert_stmt->bindParam(':brand_name', $item['brand_name']);
            $insert_stmt->bindParam(':price', $item['price']);
            $insert_stmt->bindParam(':prod_measure', $item['prod_measure']);
            $insert_stmt->bindParam(':pack_type', $item['pack_type']);
            $insert_stmt->bindParam(':expiry_date', $item['expiry_date']);
            $insert_stmt->bindParam(':delivered_date', $item['delivered_date']);
            $insert_stmt->bindParam(':country', $item['country']);
            $insert_stmt->bindParam(':batch_code', $item['batch_code']);
            $insert_stmt->bindParam(':stocks', $item['stocks']);
            $insert_stmt->execute();
        }

        // Commit transaction
        $conn->commit();

        // Fetch the newly inserted data
        $inventory_products_stmt = $conn->prepare($inventory_products_sql);
        $inventory_products_stmt->execute();
        $inventory_products_result = $inventory_products_stmt->fetchAll(PDO::FETCH_ASSOC);

        // Also update brand inventory data
        $brand_inventory_stmt = $conn->prepare($brand_inventory_sql);
        $brand_inventory_stmt->execute();
        $brand_inventory_result = $brand_inventory_stmt->fetchAll(PDO::FETCH_ASSOC);
    }
} catch (Exception $e) {
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    $inventory_products_result = [];
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports & Dashboards</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.css">
    <style>
        .dashboard-card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
            margin-bottom: 25px;
            background-color: #f15b31;
            color: white;
            border: 2px solid #d14426;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            overflow: hidden;
            width: 100%;
            position: relative;
        }

        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }

        .dashboard-card h5 {
            background-color: transparent;
            color: white;
            padding: 15px 20px;
            margin: 0 0 20px 0;
            border-radius: 0;
            border-bottom: none;
            font-weight: bold;
            text-align: center;
            word-wrap: break-word;
        }

        .dashboard-card .table-responsive {
            margin: 0;
            border-radius: 8px;
            overflow-x: auto;
            overflow-y: hidden;
        }

        .dashboard-card .table {
            background-color: white;
            color: black;
            border-radius: 8px;
            margin-bottom: 0;
            width: 100%;
            min-width: 600px;
        }

        .dashboard-card .table th {
            background-color: #d14426;
            color: white;
            border: none;
            padding: 12px;
            font-weight: bold;
        }

        .dashboard-card .table td {
            padding: 10px 12px;
            border-color: #e9ecef;
        }

        /* Individual brand cards styling - OVERRIDE BOOTSTRAP */
        .dashboard-card .card,
        .dashboard-card .card.border-primary,
        .dashboard-card .card.border-success,
        .dashboard-card .card.border-danger,
        .dashboard-card .card.border-info {
            background-color: transparent !important;
            color: black !important;
            border: 2px solid rgba(255, 255, 255, 0.3) !important;
            border-radius: 10px !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
            margin-bottom: 15px !important;
            width: 100% !important;
            max-width: 100% !important;
            overflow: hidden !important;
        }

        .dashboard-card .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        }

        .dashboard-card .card .card-header,
        .dashboard-card .card .card-header.bg-primary,
        .dashboard-card .card .card-header.bg-success,
        .dashboard-card .card .card-header.bg-danger,
        .dashboard-card .card .card-header.bg-info {
            background-color: rgba(255, 255, 255, 0.2) !important;
            color: white !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.3) !important;
            padding: 12px 15px !important;
            text-align: center !important;
        }

        .dashboard-card .card .card-body {
            background-color: transparent !important;
            color: black !important;
            padding: 15px !important;
            text-align: center !important;
        }

        .dashboard-card .card .card-body h6 {
            color: black !important;
            font-size: 0.9rem !important;
            margin-bottom: 5px !important;
            text-align: center !important;
        }

        .dashboard-card .card .card-body h4 {
            color: black !important;
            font-weight: bold !important;
            margin-bottom: 10px !important;
            text-align: center !important;
        }

        .dashboard-card .card .card-body .col-6 {
            text-align: center !important;
        }

        .dashboard-card .card .card-body .row {
            text-align: center !important;
        }

        /* Remove background from h5 elements inside brand card headers */
        .dashboard-card .card .card-header h5 {
            background-color: transparent !important;
            color: white;
            margin: 0;
            padding: 0;
            border: none;
            border-radius: 0;
        }

        /* Table styling improvements */
        .dashboard-card .table tbody tr {
            background-color: white;
            color: black;
        }

        .dashboard-card .table tbody tr:nth-child(odd) {
            background-color: #f8f9fa;
        }

        .dashboard-card .table tbody tr:hover {
            background-color: #fff3f0;
        }

        /* Style the totals row */
        .dashboard-card .table .table-dark {
            background-color: #d14426 !important;
            color: white !important;
        }

        .dashboard-card .table .table-dark td {
            background-color: #d14426 !important;
            color: white !important;
            font-weight: bold;
        }

        .stat-card {
            background: linear-gradient(45deg, #4b6cb7, #182848);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 20px;
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            overflow: hidden;
        }

        .dashboard-card .chart-container canvas {
            max-width: 100%;
            height: auto !important;
        }

        /* Ensure content doesn't overflow */
        .dashboard-card .p-3 {
            padding: 15px !important;
            overflow: hidden;
        }

        .dashboard-card .row {
            margin: 0 -5px;
        }

        .dashboard-card .col-md-4,
        .dashboard-card .col-6 {
            padding-left: 5px;
            padding-right: 5px;
        }

        /* Fix brand cards container */
        .dashboard-card .row.mb-4 {
            margin: 0 -10px 20px -10px;
        }

        .dashboard-card .col-md-4.mb-3 {
            padding: 0 10px;
        }

        /* Title styling with orange background */
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            background-color: #f15b31;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: inline-block;
            min-width: 200px;
            text-align: center;
        }

        /* Print Styles */
        @media print {
            .no-print {
                display: none !important;
            }

            .dashboard-card {
                box-shadow: none;
                border: 1px solid #ddd;
                page-break-inside: avoid;
            }

            .stat-card {
                background: none !important;
                color: black !important;
                border: 1px solid #ddd;
            }

            .chart-container {
                height: 200px;
            }

            body {
                padding: 20px;
            }

            .container {
                width: 100%;
                max-width: none;
            }
        }
    </style>
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">Reports & Dashboards</a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">Dashboard</a>
                <a class="nav-link" href="logout.php">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Report Type Selector -->
        <div class="row mb-4 no-print">
            <div class="col">
                <div class="btn-group">
                    <a href="?type=daily" class="btn btn-outline-primary <?php echo $report_type == 'daily' ? 'active' : ''; ?>">Daily</a>
                    <a href="?type=weekly" class="btn btn-outline-primary <?php echo $report_type == 'weekly' ? 'active' : ''; ?>">Weekly</a>
                    <a href="?type=monthly" class="btn btn-outline-primary <?php echo $report_type == 'monthly' ? 'active' : ''; ?>">Monthly</a>
                    <a href="?type=annual" class="btn btn-outline-primary <?php echo $report_type == 'annual' ? 'active' : ''; ?>">Annual</a>
                </div>
                <input type="date" class="form-control d-inline-block w-auto ms-2" id="datePicker" value="<?php echo $date_range; ?>">
                <button onclick="window.print()" class="btn btn-primary ms-2">
                    <i class="fas fa-print"></i> Print Report
                </button>
            </div>
        </div>

        <!-- Sales Overview -->
        <div class="row">
            <div class="col-md-3">
                <div class="stat-card">
                    <h6>Total Sales</h6>
                    <h3>$<?php
                            $total = 0;
                            if ($sales_result) {
                                foreach ($sales_result as $row) {
                                    $total += $row['total_sales'];
                                }
                            }
                            echo number_format($total, 2);
                            ?></h3>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <h6>Total Orders</h6>
                    <h3><?php
                        $orders = 0;
                        if ($sales_result) {
                            foreach ($sales_result as $row) {
                                $orders += $row['total_orders'];
                            }
                        }
                        echo number_format($orders);
                        ?></h3>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <h6>Average Order Value</h6>
                    <h3>$<?php
                            echo $orders > 0 ? number_format($total / $orders, 2) : '0.00';
                            ?></h3>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <h6>Total Products</h6>
                    <h3><?php
                        // Use inventory data directly
                        $inventory_rows = $inventory_result;
                        $products = 0;
                        foreach ($inventory_rows as $row) {
                            $products += $row['total_products'];
                        }
                        echo number_format($products);
                        ?></h3>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <h6>Total Stock Quantity</h6>
                    <h3><?php
                        $total_stock = 0;
                        foreach ($inventory_rows as $row) {
                            $total_stock += $row['total_stock'];
                        }
                        echo number_format($total_stock);
                        ?></h3>
                </div>
            </div>
        </div>

        <!-- Charts -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="dashboard-card p-3">
                    <h5>Sales Trend</h5>
                    <div class="chart-container">
                        <canvas id="salesChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="dashboard-card p-3">
                    <h5>Inventory by Brand</h5>
                    <div class="chart-container">
                        <canvas id="inventoryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Tables -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="dashboard-card p-3">
                    <h5>Top Selling Products</h5>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Brand</th>
                                    <th>Quantity</th>
                                    <th>Sales</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                if ($top_products_result) {
                                    foreach ($top_products_result as $product): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($product['product_name']); ?></td>
                                            <td><?php echo htmlspecialchars($product['brand']); ?></td>
                                            <td><?php echo number_format($product['total_quantity']); ?></td>
                                            <td>$<?php echo number_format($product['total_sales'], 2); ?></td>
                                        </tr>
                                <?php endforeach;
                                } ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="dashboard-card p-3">
                    <h5>User Statistics by Department</h5>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Department</th>
                                    <th>Total Users</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                if ($users_result) {
                                    foreach ($users_result as $dept): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($dept['department']); ?></td>
                                            <td><?php echo number_format($dept['total_users']); ?></td>
                                        </tr>
                                <?php endforeach;
                                } ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Sales Transactions Table -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="dashboard-card p-3">
                    <h5>Detailed Sales Transactions</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>Order #</th>
                                    <th>Date</th>
                                    <th>Customer</th>
                                    <th>Department</th>
                                    <th>Product</th>
                                    <th>Brand</th>
                                    <th>Quantity</th>
                                    <th>Unit Price</th>
                                    <th>Total</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                if (count($transactions_result) > 0) {
                                    foreach ($transactions_result as $transaction) {
                                        echo '<tr>';
                                        echo '<td>' . htmlspecialchars($transaction['order_number']) . '</td>';
                                        echo '<td>' . date('Y-m-d', strtotime($transaction['order_date'])) . '</td>';
                                        echo '<td>' . htmlspecialchars($transaction['customer_name']) . '</td>';
                                        echo '<td>' . htmlspecialchars($transaction['department']) . '</td>';
                                        echo '<td>' . htmlspecialchars($transaction['product_name']) . '</td>';
                                        echo '<td>' . htmlspecialchars($transaction['brand']) . '</td>';
                                        echo '<td>' . number_format($transaction['quantity']) . '</td>';
                                        echo '<td>$' . number_format($transaction['unit_price'], 2) . '</td>';
                                        echo '<td>$' . number_format($transaction['total_price'], 2) . '</td>';
                                        echo '<td>' . ucfirst(str_replace('_', ' ', $transaction['status'])) . '</td>';
                                        echo '</tr>';
                                    }
                                } else {
                                    echo '<tr><td colspan="10" class="text-center">No transactions found for the selected period</td></tr>';
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Brand Inventory Summary -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="dashboard-card p-3">
                    <h5 class="mb-3">Brand Inventory Summary</h5>

                    <!-- Brand Inventory Cards -->
                    <div class="row mb-4">
                        <?php
                        if (count($brand_inventory_result) > 0) {
                            foreach ($brand_inventory_result as $brand) {
                                $brandColor = '';
                                switch ($brand['brand_name']) {
                                    case 'EuroSpice':
                                        $brandColor = 'primary';
                                        $brandIcon = 'fa-leaf';
                                        break;
                                    case 'VISKASE':
                                        $brandColor = 'success';
                                        $brandIcon = 'fa-pepper-hot';
                                        break;
                                    case 'SpiceWorld':
                                        $brandColor = 'danger';
                                        $brandIcon = 'fa-mortar-pestle';
                                        break;
                                    default:
                                        $brandColor = 'info';
                                        $brandIcon = 'fa-box';
                                }
                        ?>
                                <div class="col-md-4 mb-3">
                                    <div class="card border-<?php echo $brandColor; ?> h-100">
                                        <div class="card-header bg-<?php echo $brandColor; ?> text-white">
                                            <h5 class="mb-0"><i class="fas <?php echo $brandIcon; ?> me-2"></i><?php echo htmlspecialchars($brand['brand_name']); ?></h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-6 mb-3">
                                                    <h6 class="text-muted">Products</h6>
                                                    <h4><?php echo number_format($brand['product_count']); ?></h4>
                                                </div>
                                                <div class="col-6 mb-3">
                                                    <h6 class="text-muted">Total Stock</h6>
                                                    <h4><?php echo number_format($brand['total_stock']); ?></h4>
                                                </div>
                                                <div class="col-6">
                                                    <h6 class="text-muted">Avg. Price</h6>
                                                    <h4>$<?php echo number_format($brand['average_price'], 2); ?></h4>
                                                </div>
                                                <div class="col-6">
                                                    <h6 class="text-muted">Total Value</h6>
                                                    <h4>$<?php echo number_format($brand['total_value'], 2); ?></h4>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        <?php
                            }
                        }
                        ?>
                    </div>

                    <!-- Brand Inventory Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead class="table-primary">
                                <tr>
                                    <th>Brand</th>
                                    <th>Product Count</th>
                                    <th>Total Stock</th>
                                    <th>Average Price</th>
                                    <th>Total Value</th>
                                    <th>Stock Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                if (count($brand_inventory_result) > 0) {
                                    $total_products = 0;
                                    $total_stocks = 0;
                                    $total_inventory_value = 0;

                                    foreach ($brand_inventory_result as $brand) {
                                        // Determine stock status
                                        $stockStatus = '';
                                        $stockClass = '';
                                        if ($brand['total_stock'] > 100) {
                                            $stockStatus = 'High';
                                            $stockClass = 'success';
                                        } elseif ($brand['total_stock'] > 50) {
                                            $stockStatus = 'Medium';
                                            $stockClass = 'warning';
                                        } else {
                                            $stockStatus = 'Low';
                                            $stockClass = 'danger';
                                        }

                                        echo '<tr>';
                                        echo '<td><strong>' . htmlspecialchars($brand['brand_name'] ?? 'Unknown') . '</strong></td>';
                                        echo '<td>' . number_format($brand['product_count']) . '</td>';
                                        echo '<td>' . number_format($brand['total_stock']) . '</td>';
                                        echo '<td>$' . number_format($brand['average_price'], 2) . '</td>';
                                        echo '<td>$' . number_format($brand['total_value'], 2) . '</td>';
                                        echo '<td><span class="badge bg-' . $stockClass . '">' . $stockStatus . '</span></td>';
                                        echo '</tr>';

                                        $total_products += $brand['product_count'];
                                        $total_stocks += $brand['total_stock'];
                                        $total_inventory_value += $brand['total_value'];
                                    }

                                    // Add totals row
                                    echo '<tr class="table-dark fw-bold">';
                                    echo '<td>TOTAL</td>';
                                    echo '<td>' . number_format($total_products) . '</td>';
                                    echo '<td>' . number_format($total_stocks) . '</td>';
                                    echo '<td>-</td>';
                                    echo '<td>$' . number_format($total_inventory_value, 2) . '</td>';
                                    echo '<td>-</td>';
                                    echo '</tr>';
                                } else {
                                    echo '<tr><td colspan="6" class="text-center">No brand inventory data available</td></tr>';
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Inventory Products Table -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="dashboard-card p-3">
                    <h5>Detailed Inventory Products</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped table-hover">
                            <thead class="table-success">
                                <tr>
                                    <th>ID</th>
                                    <th>Product Name</th>
                                    <th>Brand</th>
                                    <th>Price</th>
                                    <th>Measurement</th>
                                    <th>Package Type</th>
                                    <th>Country</th>
                                    <th>Batch Code</th>
                                    <th>Stock</th>
                                    <th>Expiry Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                if (count($inventory_products_result) > 0) {
                                    foreach ($inventory_products_result as $product) {
                                        $expiry_date = !empty($product['expiry_date']) ? date('Y-m-d', strtotime($product['expiry_date'])) : 'N/A';

                                        echo '<tr>';
                                        echo '<td>' . htmlspecialchars($product['id']) . '</td>';
                                        echo '<td>' . htmlspecialchars($product['prod_name']) . '</td>';
                                        echo '<td>' . htmlspecialchars($product['brand_name']) . '</td>';
                                        echo '<td>$' . number_format($product['price'], 2) . '</td>';
                                        echo '<td>' . htmlspecialchars($product['prod_measure'] ?? 'N/A') . '</td>';
                                        echo '<td>' . htmlspecialchars($product['pack_type'] ?? 'N/A') . '</td>';
                                        echo '<td>' . htmlspecialchars($product['country'] ?? 'N/A') . '</td>';
                                        echo '<td>' . htmlspecialchars($product['batch_code'] ?? 'N/A') . '</td>';
                                        echo '<td>' . number_format($product['stocks']) . '</td>';
                                        echo '<td>' . $expiry_date . '</td>';
                                        echo '</tr>';
                                    }
                                } else {
                                    echo '<tr><td colspan="10" class="text-center">No inventory products available</td></tr>';
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Low Stock Items Table -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="dashboard-card p-3">
                    <h5>Low Stock Items (Stock &lt; 10)</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Product Name</th>
                                    <th>Brand</th>
                                    <th>Stock</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                try {
                                    $low_stock_sql = "SELECT name, brand, stock FROM products WHERE stock < 10 ORDER BY stock ASC";
                                    $low_stock_stmt = $conn->prepare($low_stock_sql);
                                    $low_stock_stmt->execute();
                                    $low_stock_items = $low_stock_stmt->fetchAll(PDO::FETCH_ASSOC);
                                } catch (Exception $e) {
                                    $low_stock_items = [];
                                }
                                if (count($low_stock_items) > 0) {
                                    foreach ($low_stock_items as $item) {
                                        echo '<tr>';
                                        echo '<td>' . htmlspecialchars($item['name'] ?? '') . '</td>';
                                        echo '<td>' . htmlspecialchars($item['brand'] ?? '') . '</td>';
                                        echo '<td>' . htmlspecialchars($item['stock'] ?? '') . '</td>';
                                        echo '</tr>';
                                    }
                                } else {
                                    echo '<tr><td colspan="3" class="text-center">No low stock items</td></tr>';
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>
    <script>
        // Date picker change handler
        document.getElementById('datePicker').addEventListener('change', function() {
            window.location.href = `?type=<?php echo $report_type; ?>&date=${this.value}`;
        });

        // Sales Chart
        const salesCtx = document.getElementById('salesChart').getContext('2d');
        new Chart(salesCtx, {
            type: 'line',
            data: {
                labels: <?php
                        $labels = [];
                        $sales = [];
                        if ($sales_result) {
                            // Create a new copy of the sales data for the chart
                            $sales_data_for_chart = $sales_result;
                            foreach ($sales_data_for_chart as $row) {
                                $labels[] = $row['period'];
                                $sales[] = $row['total_sales'];
                            }
                        }
                        echo json_encode(array_reverse($labels));
                        ?>,
                datasets: [{
                    label: 'Sales',
                    data: <?php echo json_encode(array_reverse($sales)); ?>,
                    borderColor: '#4b6cb7',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // Inventory Chart
        const inventoryCtx = document.getElementById('inventoryChart').getContext('2d');
        new Chart(inventoryCtx, {
            type: 'pie',
            data: {
                labels: <?php
                        $brands = [];
                        $stocks = [];
                        foreach ($brand_inventory_result as $row) {
                            $brands[] = $row['brand_name'];
                            $stocks[] = $row['total_stock'];
                        }
                        echo json_encode($brands);
                        ?>,
                datasets: [{
                    data: <?php echo json_encode($stocks); ?>,
                    backgroundColor: [
                        '#4b6cb7',
                        '#182848',
                        '#2c3e50',
                        '#3498db',
                        '#2980b9'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    </script>

    <!-- Back to Dashboard Button -->
    <div class="text-center mt-4 mb-4">
        <a href="../admin/admin_dashboard.php" class="btn" style="background-color: #f15b31; color: white;">Back to Dashboard</a>
    </div>
</body>

</html>